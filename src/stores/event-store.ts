import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Event, EventFormData, EventFilters } from 'src/models/event';
import { EventStatus, EventType } from 'src/models/event';

export const useEventStore = defineStore('event', () => {
  // State
  const events = ref<Event[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Mock data for development
  const mockEvents: Event[] = [
    {
      id: '1',
      title: 'Consul<PERSON>',
      description: 'Consulta de rotina',
      startDate: '2024-08-19T09:00:00.000Z',
      endDate: '2024-08-19T10:00:00.000Z',
      patientId: '1',
      patientName: '<PERSON>',
      type: EventType.CONSULTATION,
      status: EventStatus.SCHEDULED,
      location: 'Consultório 1',
      notes: 'Paciente com histórico de hipertensão',
      createdAt: '2024-08-15T10:00:00Z',
      updatedAt: '2024-08-15T10:00:00Z',
    },
    {
      id: '2',
      title: '<PERSON><PERSON><PERSON>',
      description: 'Retorno pós-cirúrgico',
      startDate: '2024-08-19T14:30:00.000Z',
      endDate: '2024-08-19T15:30:00.000Z',
      patientId: '2',
      patientName: 'Maria Santos',
      type: EventType.FOLLOW_UP,
      status: EventStatus.CONFIRMED,
      location: 'Consultório 2',
      notes: 'Verificar cicatrização',
      createdAt: '2024-08-16T11:00:00Z',
      updatedAt: '2024-08-16T11:00:00Z',
    },
    {
      id: '3',
      title: 'Exame Pedro Oliveira',
      description: 'Exame de sangue',
      startDate: '2024-08-20T08:00:00.000Z',
      endDate: '2024-08-20T08:30:00.000Z',
      patientId: '3',
      patientName: 'Pedro Oliveira',
      type: EventType.EXAM,
      status: EventStatus.SCHEDULED,
      location: 'Laboratório',
      notes: 'Jejum de 12 horas',
      createdAt: '2024-08-17T09:15:00Z',
      updatedAt: '2024-08-17T09:15:00Z',
    },
    {
      id: '4',
      title: 'Reunião Equipe',
      description: 'Reunião semanal da equipe médica',
      startDate: '2024-08-21T16:00:00.000Z',
      endDate: '2024-08-21T17:00:00.000Z',
      type: EventType.MEETING,
      status: EventStatus.SCHEDULED,
      location: 'Sala de Reuniões',
      notes: 'Discussão de casos clínicos',
      createdAt: '2024-08-18T10:00:00Z',
      updatedAt: '2024-08-18T10:00:00Z',
    },
    {
      id: '5',
      title: 'Cirurgia Ana Costa',
      description: 'Cirurgia de vesícula',
      startDate: '2024-08-22T07:00:00.000Z',
      endDate: '2024-08-22T09:00:00.000Z',
      patientId: '4',
      patientName: 'Ana Costa',
      type: EventType.SURGERY,
      status: EventStatus.CONFIRMED,
      location: 'Centro Cirúrgico',
      notes: 'Jejum absoluto 8h antes',
      createdAt: '2024-08-10T14:00:00Z',
      updatedAt: '2024-08-18T16:30:00Z',
    },
  ];

  // Getters
  const todayEvents = computed(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return events.value.filter((event) => {
      const eventDate = new Date(event.startDate);
      return eventDate >= today && eventDate < tomorrow;
    });
  });

  const upcomingEvents = computed(() => {
    const now = new Date();
    return events.value
      .filter((event) => new Date(event.startDate) > now)
      .sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime())
      .slice(0, 5);
  });

  const eventsByStatus = computed(() => {
    return events.value.reduce(
      (acc, event) => {
        if (!acc[event.status]) {
          acc[event.status] = [];
        }
        acc[event.status].push(event);
        return acc;
      },
      {} as Record<EventStatus, Event[]>,
    );
  });

  const eventsByType = computed(() => {
    return events.value.reduce(
      (acc, event) => {
        if (!acc[event.type]) {
          acc[event.type] = [];
        }
        acc[event.type].push(event);
        return acc;
      },
      {} as Record<EventType, Event[]>,
    );
  });

  const totalEvents = computed(() => events.value.length);

  // Actions
  const loadEvents = async (filters?: EventFilters): Promise<void> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 300));

      let filteredEvents = [...mockEvents];

      if (filters) {
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          filteredEvents = filteredEvents.filter(
            (event) =>
              event.title.toLowerCase().includes(searchLower) ||
              event.description?.toLowerCase().includes(searchLower) ||
              event.patientName?.toLowerCase().includes(searchLower),
          );
        }

        if (filters.type) {
          filteredEvents = filteredEvents.filter((event) => event.type === filters.type);
        }

        if (filters.status) {
          filteredEvents = filteredEvents.filter((event) => event.status === filters.status);
        }

        if (filters.patientId) {
          filteredEvents = filteredEvents.filter((event) => event.patientId === filters.patientId);
        }

        if (filters.startDate && filters.endDate) {
          const startDate = new Date(filters.startDate);
          const endDate = new Date(filters.endDate);
          filteredEvents = filteredEvents.filter((event) => {
            const eventDate = new Date(event.startDate);
            return eventDate >= startDate && eventDate <= endDate;
          });
        }
      }

      events.value = filteredEvents;
    } catch (err) {
      error.value = 'Erro ao carregar eventos';
      console.error('Error loading events:', err);
    } finally {
      loading.value = false;
    }
  };

  const getEventById = (id: string): Event | undefined => {
    return events.value.find((event) => event.id === id);
  };

  const getEventsByDate = (date: Date): Event[] => {
    const targetDate = new Date(date);
    targetDate.setHours(0, 0, 0, 0);
    const nextDay = new Date(targetDate);
    nextDay.setDate(nextDay.getDate() + 1);

    return events.value.filter((event) => {
      const eventDate = new Date(event.startDate);
      return eventDate >= targetDate && eventDate < nextDay;
    });
  };

  const getEventsByDateRange = (startDate: Date, endDate: Date): Event[] => {
    return events.value.filter((event) => {
      const eventStart = new Date(event.startDate);
      const eventEnd = new Date(event.endDate);

      return (
        (eventStart >= startDate && eventStart <= endDate) ||
        (eventEnd >= startDate && eventEnd <= endDate) ||
        (eventStart <= startDate && eventEnd >= endDate)
      );
    });
  };

  const createEvent = async (eventData: EventFormData): Promise<Event> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Get patient name if patientId is provided
      let patientName: string | undefined;
      if (eventData.patientId) {
        // This would normally be a call to get patient data
        // For now, we'll use a simple lookup from mock data
        const mockPatients = [
          { id: '1', name: 'João Silva' },
          { id: '2', name: 'Maria Santos' },
          { id: '3', name: 'Pedro Oliveira' },
          { id: '4', name: 'Ana Costa' },
        ];
        const patient = mockPatients.find((p) => p.id === eventData.patientId);
        patientName = patient?.name;
      }

      const newEvent: Event = {
        id: Date.now().toString(),
        ...eventData,
        patientName,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      events.value.push(newEvent);
      mockEvents.push(newEvent); // Update mock data

      return newEvent;
    } catch (err) {
      error.value = 'Erro ao criar evento';
      console.error('Error creating event:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateEvent = async (id: string, eventData: Partial<EventFormData>): Promise<Event> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const eventIndex = events.value.findIndex((event) => event.id === id);
      if (eventIndex === -1) {
        throw new Error('Evento não encontrado');
      }

      const updatedEvent: Event = {
        ...events.value[eventIndex],
        ...eventData,
        updatedAt: new Date().toISOString(),
      };

      events.value[eventIndex] = updatedEvent;

      // Update mock data
      const mockIndex = mockEvents.findIndex((event) => event.id === id);
      if (mockIndex !== -1) {
        mockEvents[mockIndex] = updatedEvent;
      }

      return updatedEvent;
    } catch (err) {
      error.value = 'Erro ao atualizar evento';
      console.error('Error updating event:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteEvent = async (id: string): Promise<void> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const eventIndex = events.value.findIndex((event) => event.id === id);
      if (eventIndex === -1) {
        throw new Error('Evento não encontrado');
      }

      events.value.splice(eventIndex, 1);

      // Update mock data
      const mockIndex = mockEvents.findIndex((event) => event.id === id);
      if (mockIndex !== -1) {
        mockEvents.splice(mockIndex, 1);
      }
    } catch (err) {
      error.value = 'Erro ao excluir evento';
      console.error('Error deleting event:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateEventStatus = async (id: string, status: EventStatus): Promise<Event> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 300));

      const eventIndex = events.value.findIndex((event) => event.id === id);
      if (eventIndex === -1) {
        throw new Error('Evento não encontrado');
      }

      const updatedEvent: Event = {
        ...events.value[eventIndex],
        status,
        updatedAt: new Date().toISOString(),
      };

      events.value[eventIndex] = updatedEvent;

      // Update mock data
      const mockIndex = mockEvents.findIndex((event) => event.id === id);
      if (mockIndex !== -1) {
        mockEvents[mockIndex] = updatedEvent;
      }

      return updatedEvent;
    } catch (err) {
      error.value = 'Erro ao alterar status do evento';
      console.error('Error updating event status:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Initialize with mock data
  events.value = [...mockEvents];

  return {
    // State
    events,
    loading,
    error,

    // Getters
    todayEvents,
    upcomingEvents,
    eventsByStatus,
    eventsByType,
    totalEvents,

    // Actions
    loadEvents,
    getEventById,
    getEventsByDate,
    getEventsByDateRange,
    createEvent,
    updateEvent,
    deleteEvent,
    updateEventStatus,
  };
});
